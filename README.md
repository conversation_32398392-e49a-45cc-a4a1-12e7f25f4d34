# 元梦之星农场自动化脚本

## 项目简介
这是一个专为元梦之星农场设计的自动化脚本，使用ADB命令模拟WASD按键操作，实现精确的角色移动控制。支持PC端调试和手机端直接执行两种模式。

## 参数说明
- 滑动屏幕转视角的时候，往左滑的越多，也就是X变得越多，按A左移的时候就会越偏下


## 核心特性
- ✅ 使用 `adb shell input keyevent --longpress` 实现稳定的按键控制
- ✅ 详细的日志记录和错误诊断
- ✅ 移动距离校准系统
- ✅ 复杂移动序列执行
- ✅ 实时ADB连接监控
- ✅ 支持移动、点击、滑动的混合操作
- ✅ 配置文件管理，支持多个预设序列
- ✅ 手机端直接执行，无需PC连接

## 环境要求

### PC端环境
1. **ADB工具**: 确保ADB已安装并添加到系统PATH
2. **Python 3.6+**: 运行脚本需要Python环境
3. **Android设备**: 开启USB调试模式并连接到电脑

### 手机端环境
1. **Android设备**: Android 4.0+
2. **权限要求**:
   - **方案一**: Root权限（传统方案）
   - **方案二**: Shizuku + 自动任务软件（无需Root，未测试）
3. **终端应用**: 推荐使用 Termux 或其他终端应用

## 快速开始

### PC端使用

#### 1. 检查ADB连接
```bash
adb devices
```
确保显示你的设备状态为 `device`

#### 2. 运行主脚本
```bash
python move_debugger.py
```

#### 3. 功能说明

**主要功能菜单：**
1. **移动测试** - 测试单个方向的移动
2. **移动距离校准** - 确定按键次数与移动距离的关系
3. **统一命令执行** - 融合移动/点击/滑动的混合操作 ⭐
4. **单次按键测试** - 验证按键是否有效
5. **ADB连接状态** - 检查设备连接
6. **屏幕信息** - 获取设备屏幕参数
7. **触摸参数记录器** - 记录触摸操作并生成命令 🆕

### 手机端使用

#### 1. 部署脚本文件
将 `auto_game.sh` 和 `config_commands.txt` 复制到手机存储中

#### 2. 设置执行权限
```bash
chmod +x auto_game.sh
```

#### 3. 基本使用
```bash
# 查看配置
sh auto_game.sh -c

# 执行所有命令行（默认行为）
sh auto_game.sh

# 执行指定行命令
sh auto_game.sh 1

# 显示帮助
sh auto_game.sh -h
```

## PC端使用流程

### 第一步：验证按键有效性
选择菜单 `5. 单次按键测试`，确认每个方向键都能让角色移动。

### 第二步：校准移动距离
选择菜单 `2. 移动距离校准`：
- 选择要校准的方向（W/A/S/D）
- 系统会测试按1次、2次、3次、5次、10次的移动效果
- 记录每次测试的移动距离
- 校准数据会保存到 `movement_calibration.txt`

### 第三步：执行统一命令
选择菜单 `3. 统一命令执行`：
- 支持移动、点击、滑动的混合操作
- 移动命令：`W3 A2 S1 D4`
- 点击命令：`540,960` (x,y坐标)
- 滑动命令：`SWIPE:800,500,800,300,500` (起点x,y,终点x,y,持续时间ms)
- 混合命令：`W3 540,960 A2 SWIPE:800,500,800,300,500`

### 第四步：使用触摸参数记录器 🆕
选择菜单 `7. 触摸参数记录器`：
- **功能**: 自动记录您的触摸操作并生成对应的命令
- **使用场景**: 当您不确定具体坐标或滑动参数时
- **操作流程**: 开始录制 → 在手机上进行操作 → 停止录制 → 查看生成的命令
- **输出格式**: 自动生成标准的TAP和SWIPE命令，可直接用于统一命令执行

## PC端功能详细说明

### 1. 移动测试
**功能**: 测试单个方向的移动效果
- **使用场景**: 验证WASD按键是否正常工作
- **操作方式**: 选择方向(W/A/S/D) → 输入移动次数 → 观察角色移动
- **注意事项**: 确保游戏处于可操作状态

### 2. 移动距离校准
**功能**: 建立按键次数与实际移动距离的对应关系
- **使用场景**: 精确控制角色移动距离
- **校准过程**: 系统自动测试1次、2次、3次、5次、10次按键的移动效果
- **数据保存**: 校准结果保存到 `movement_calibration.txt`
- **实用价值**: 为复杂路径规划提供数据基础

### 3. 统一命令执行 ⭐
**功能**: 执行复杂的混合操作序列
- **支持命令类型**:
  - 移动: `W3 A2 S1 D4` (方向+次数)
  - 点击: `540,960` (x,y坐标)
  - 滑动: `SWIPE:800,500,800,300,500` (起点,终点,时长)
  - 延迟: `1000ms` (等待时间)
- **混合示例**: `W3 1000ms 540,960 500ms SWIPE:800,500,800,300,500`
- **执行特点**: 按顺序执行，支持自定义间隔时间

### 4. 单次按键测试
**功能**: 验证单个按键的响应效果
- **使用场景**: 排查按键失效问题
- **测试范围**: W(上) A(左) S(下) D(右) J(动作键)
- **诊断价值**: 快速定位按键映射问题

### 5. ADB连接状态
**功能**: 检查和诊断ADB连接
- **显示信息**: 连接设备列表、设备状态
- **故障诊断**: 自动检测常见连接问题
- **使用时机**: 程序启动前、操作失效时

### 6. 屏幕信息
**功能**: 获取设备屏幕参数
- **显示内容**: 屏幕分辨率、密度、方向
- **实用价值**: 为坐标计算提供基础数据
- **兼容性**: 支持不同分辨率设备的坐标适配

### 7. 触摸参数记录器 🆕
**功能**: 自动记录触摸操作并生成命令
- **子功能菜单**:
  1. **诊断getevent可用性** - 检查触摸事件监听环境
  2. **开始记录触摸事件** - 基础录制模式，记录点击和滑动
  3. **专用录制模式** - 实验性功能（已停用）
  4. **坐标转换诊断** - 分析坐标映射关系
  5. **手动记录坐标** - 备选的手动输入方案
  6. **查看已记录的命令** - 显示录制结果
  7. **保存命令到文件** - 导出为文本和JSON格式
  8. **清空记录** - 重置录制数据
  9. **测试生成的命令** - 验证录制结果的可用性

#### 触摸记录器使用流程:
1. **环境检查**: 选择"诊断getevent可用性"确保系统支持
2. **开始录制**: 选择"开始记录触摸事件"
3. **进行操作**: 在手机屏幕上进行您需要的点击或滑动操作
4. **停止录制**: 按Ctrl+C停止录制
5. **查看结果**: 选择"查看已记录的命令"检查生成的命令
6. **测试验证**: 选择"测试生成的命令"验证操作效果
7. **保存使用**: 选择"保存命令到文件"导出，然后在统一命令执行中使用

#### 技术特点:
- **智能识别**: 自动区分点击和滑动操作（基于移动距离）
- **坐标转换**: 自动处理触摸传感器坐标到屏幕坐标的转换
- **格式兼容**: 生成的命令可直接用于统一命令执行功能
- **双格式输出**: 同时生成文本和JSON格式，满足不同使用需求

## 手机端使用流程

### 方法一: 使用Termux + Root权限 (传统方案)

1. **安装Termux**
   - 从Google Play或F-Droid下载安装Termux

2. **获取Root权限**
   ```bash
   # 在Termux中安装tsu (需要设备已root)
   pkg install tsu
   ```

3. **上传脚本文件**
   - 将脚本文件复制到手机存储
   - 可以通过USB、云盘、或直接在Termux中创建

4. **设置执行权限**
   ```bash
   chmod +x auto_game.sh
   ```

### 方法二: 使用Shizuku + 自动任务软件 (无Root方案，未测试)

**注意**: 此方案理论可行但未经测试，仅供参考

1. **安装Shizuku**
   - 从Google Play或GitHub下载安装Shizuku
   - 通过ADB或无线调试激活Shizuku服务

2. **安装自动任务软件**
   - 推荐: Tasker、MacroDroid、Automate等
   - 确保软件支持Shizuku权限调用

3. **配置权限**
   ```bash
   # 通过ADB激活Shizuku (电脑端执行)
   adb shell sh /sdcard/Android/data/moe.shizuku.privileged.api/start.sh
   ```

4. **创建自动任务**
   - 在自动任务软件中创建Shell命令任务
   - 使用Shizuku权限执行input命令
   - 将auto_game.sh中的命令逐一配置为任务

5. **优势与限制**
   - ✅ 无需Root权限
   - ✅ 相对安全，权限可控
   - ❌ 配置复杂，需要一定技术基础
   - ❌ 依赖第三方软件，稳定性待验证

### 方法三: 直接在设备上创建 (Root方案)

1. **打开终端应用**
2. **切换到root用户**
   ```bash
   su
   ```
3. **创建脚本文件**
   ```bash
   # 复制脚本内容到文件中
   cat > auto_game.sh << 'EOF'
   # (粘贴脚本内容)
   EOF
   ```

## 统一命令语法

### 移动命令
```
方向键 + 次数
```
- `W5` - 向上移动5次
- `A3 D2` - 向左3次，然后向右2次
- 方向键：W(上) A(左) S(下) D(右)

### 点击命令
```
x,y坐标
```
- `540,960` - 点击坐标(540, 960)
- `100,200 800,600` - 连续点击两个位置

### 滑动命令
```
SWIPE:x1,y1,x2,y2,duration
```
- `SWIPE:800,500,800,300,500` - 从(800,500)滑动到(800,300)，持续500ms
- 主要用于游戏视角转动，推荐使用屏幕右侧区域
- duration影响滑动速度：值越小滑动越快

### 间隔时间参数
```
数字 + ms后缀
```
- `500ms` - 等待500毫秒
- `1000ms` - 等待1000毫秒（1秒）
- `2000ms` - 等待2000毫秒（2秒）
- 间隔时间参数应用到前一个命令之后
- 如果不指定，使用默认的800ms间隔

### 混合命令示例
- `W3 540,960` - 向上3次然后点击中心（默认间隔）
- `W3 1000ms 540,960` - 向上3次→等待1000ms→点击中心
- `A2 500ms SWIPE:800,600,800,300,400 D1` - 向左2次→等待500ms→滑动→向右1次
- `2160,980 500ms D3 500ms 1990,600 2000ms 2000,860` - 复杂的定时序列操作

## 配置参数

### 时间间隔参数
脚本中有几个重要的时间间隔参数，可以根据需要调整：

```bash
# 时间间隔参数 (单位: 秒)
DEFAULT_INTERVAL=0.8    # 命令之间的默认间隔 (800ms)
KEY_INTERVAL=0.8        # 按键之间的间隔 (800ms)
SEQ_INTERVAL=2.0        # 命令序列之间的间隔 (2000ms)
```

#### 参数使用场景

**DEFAULT_INTERVAL (0.8秒)**
- **用途**: 不同命令之间的间隔
- **使用场景**:
  - 点击命令后的等待
  - 滑动命令后的等待
  - 移动命令后的等待

**KEY_INTERVAL (0.8秒)**
- **用途**: 同一移动命令中多次按键之间的间隔
- **使用场景**:
  - W3 命令中，3次W键按压之间的间隔
  - A2 命令中，2次A键按压之间的间隔

**SEQ_INTERVAL (2.0秒)**
- **用途**: 不同命令序列之间的间隔
- **使用场景**:
  - auto_game.sh 中执行完一行命令后，开始下一行命令前的等待
  - 给用户足够时间观察游戏状态变化

### 配置文件格式

配置文件 `config_commands.txt` 的格式如下：

1. 每行一个命令序列，清晰易维护
2. 以`#`开头的行会被视为注释
3. 空行会被自动忽略

例如：
```
# 农场第六列
2160,980 3000ms D3 1990,600 2000,860 2000ms SWIPE:2000,500,2070,500,500 3000ms A4 3000ms A4 3000ms A4 3000ms A3 3000ms

# 农场第五列
2160,980 3000ms D3 1990,600 SWIPE:2000,500,1980,510,500 2000,860 2000ms SWIPE:2000,500,2070,500,500 3000ms A4 3000ms A4 3000ms A4 3000ms A3 3000ms
```

### 手机端使用方法

```bash
# 查看配置
sh auto_game.sh -c

# 执行所有命令行（默认行为）
sh auto_game.sh

# 执行农场第六列
sh auto_game.sh 1

# 执行农场第五列
sh auto_game.sh 2

# 执行农场第四列
sh auto_game.sh 3

# 执行农场第三列
sh auto_game.sh 4

# 执行农场第二列
sh auto_game.sh 5

# 执行农场第一列
sh auto_game.sh 6

# 执行简单移动测试
sh auto_game.sh 7

# 执行点击测试
sh auto_game.sh 8

# 执行滑动测试
sh auto_game.sh 9

# 显示帮助
sh auto_game.sh -h
```

### 配置文件管理

```bash
# 编辑配置文件添加新的命令序列
vi config_commands.txt

# 或使用其他编辑器
nano config_commands.txt

# 在文件末尾添加新的命令序列
echo "W5 A3 540,960 D2" >> config_commands.txt

# 查看当前配置
sh auto_game.sh -c
```

## 文件说明

### 核心文件
- `move_debugger.py` - PC端主脚本，包含所有调试和测试功能
- `auto_game.sh` - 手机端执行脚本，支持配置文件管理
- `config_commands.txt` - 命令配置文件，存储预设的操作序列
- `README.md` - 完整的使用说明文档

### 生成的文件
- `move_debugger.log` - 详细的操作日志（PC端）
- `movement_calibration.txt` - 移动距离校准数据（PC端）

### 执行流程说明

#### 执行所有命令行（默认）
当执行 `sh auto_game.sh` 时：

1. 读取配置文件所有有效命令行（跳过注释和空行）
2. 依次执行每一行：
   - 执行农场第六列 → 等待2秒
   - 执行农场第五列 → 等待2秒
   - 执行农场第四列 → 等待2秒
   - 执行农场第三列 → 等待2秒
   - 执行农场第二列 → 等待2秒
   - 执行农场第一列 → 等待2秒
   - 执行简单移动测试 → 等待2秒
   - 执行点击测试 → 等待2秒
   - 执行滑动测试 → 完成

#### 执行单行命令
当执行 `sh auto_game.sh 1` 时：

1. 读取配置文件第1行有效命令（跳过注释和空行）
2. 解析该行的所有命令
3. 依次执行每个命令：
   - 点击 (2160,980)
   - 等待 3000ms
   - 向右移动 3 次
   - 点击 (1990,600)
   - ...以此类推

#### 当前配置文件包含
- 第1行：农场第六列
- 第2行：农场第五列
- 第3行：农场第四列
- 第4行：农场第三列
- 第5行：农场第二列
- 第6行：农场第一列
- 第7行：简单移动测试
- 第8行：点击测试
- 第9行：滑动测试

## 技术原理

### 为什么选择keyevent而不是swipe？
1. **精确性**: keyevent是离散事件，移动距离更可控
2. **稳定性**: 不受滑动时间和速度影响
3. **重现性**: 相同的按键次数产生相同的移动效果

### 为什么使用--longpress？
经过测试发现，普通的keyevent无法让游戏角色移动，只有longpress方法才能被游戏识别为有效的按键输入。

### 参数一致性保证

#### 键位映射
**auto_game.sh** 和 **move_debugger.py** 中的键位映射完全一致：

```
W键: keycode=51  # 向上移动
A键: keycode=29  # 向左移动
S键: keycode=47  # 向下移动
D键: keycode=32  # 向右移动
```

#### 按键执行方法
两个文件都使用相同的按键执行方法：

**auto_game.sh**:
```bash
input keyevent --longpress $keycode
```

**move_debugger.py**:
```python
adb shell input keyevent --longpress {keycode}
```

#### 参数验证方法
可以通过以下方式验证参数一致性：

1. **运行 move_debugger.py**:
   ```bash
   python move_debugger.py
   ```
   选择移动测试，观察间隔时间是否为0.8秒

2. **运行 auto_game.sh**:
   ```bash
   sh auto_game.sh 7  # 执行简单移动测试
   ```
   观察移动命令的执行间隔

3. **对比日志输出**:
   两个程序的日志应该显示相同的时间间隔值

## 故障排除

### PC端常见问题
1. **角色不移动**
   - 确认使用的是longpress方法
   - 检查游戏是否处于可移动状态
   - 验证按键映射是否正确

2. **ADB连接失败**
   - 检查USB调试是否开启
   - 确认ADB驱动是否正确安装
   - 尝试重新连接设备

3. **按键无响应**
   - 确认游戏窗口处于焦点状态
   - 检查设备是否锁屏
   - 验证keycode是否正确

### 手机端常见问题

1. **权限被拒绝**
   ```bash
   # 解决方案: 确保有root权限
   su
   # 然后再执行脚本
   ```

2. **命令不存在**
   ```bash
   # 检查input命令是否可用
   which input
   # 或者
   input --help
   ```

3. **脚本无法执行**
   ```bash
   # 检查文件权限
   ls -l *.sh
   # 设置执行权限
   chmod +x *.sh
   ```

4. **游戏无响应**
   - 确认游戏处于前台运行状态
   - 检查坐标是否正确
   - 验证按键映射是否有效

### 调试技巧

#### PC端调试
- 查看 `move_debugger.log` 获取详细错误信息
- 使用单次按键测试验证每个方向
- 逐步增加移动次数，观察效果变化

#### 手机端调试

1. **测试单个命令**
   ```bash
   # 测试点击
   input tap 540 960

   # 测试按键
   input keyevent --longpress 51

   # 测试滑动
   input swipe 800 500 800 300 500
   ```

2. **查看日志输出**
   - 脚本会输出详细的执行日志
   - 观察每个步骤的执行情况

3. **分段测试**
   - 将复杂序列分解为小段测试
   - 逐步验证每个操作的效果

### 权限要求
- **PC端**: 需要ADB连接权限和USB调试权限
- **手机端权限方案**:
  - **方案一**: Root权限 + 终端应用（传统方案，稳定可靠）
  - **方案二**: Shizuku权限 + 自动任务软件（无Root方案，未测试）
- **权限验证**: 确保选择的方案能够执行`input`命令

### 游戏兼容性
- 脚本使用Android标准input命令
- 兼容大部分Android游戏
- 某些游戏可能有反作弊检测

### 坐标适配
- 脚本中的坐标基于特定屏幕分辨率
- 不同设备可能需要调整坐标值
- 建议先测试单个操作确认坐标准确性

## 推荐终端应用

### Termux (推荐)
- **优点**: 功能强大，支持包管理
- **下载**: Google Play Store 或 F-Droid
- **特点**: 无需root即可使用基本功能

### Terminal Emulator
- **优点**: 轻量级，启动快速
- **适用**: 简单脚本执行
- **特点**: 占用空间小

### ADB Shell
- **优点**: 通过USB连接使用
- **适用**: 开发调试环境
- **特点**: 无需在手机上安装额外应用

## 扩展功能

### 自动化脚本开发
基于校准数据，可以开发更复杂的自动化功能：
- 自动收菜路径规划
- 定时任务执行
- 多点位巡逻
- 资源采集优化

### 虚拟手柄
未来可以开发图形界面，提供：
- 屏幕上的WASD控制按钮
- 实时移动距离显示
- 路径录制和回放功能

## 注意事项

### 使用建议
1. **首次使用前**
   - 先在测试环境验证脚本效果
   - 确认所有坐标和操作的准确性

2. **使用过程中**
   - 保持游戏处于前台状态
   - 避免在脚本执行时手动操作

3. **安全使用**
   - 遵守游戏服务条款
   - 合理控制自动化频率
   - 定期备份游戏数据

### 参数修改注意事项
1. **参数修改**: 如需调整时间间隔，请同时修改两个文件中的对应参数
2. **测试验证**: 修改参数后，建议先用 move_debugger.py 测试效果，再用 auto_game.sh 执行实际任务
3. **备份配置**: 重要参数修改前建议备份原始配置

### 技术支持
如果遇到问题，请检查：
1. 设备是否已正确root（手机端）
2. ADB连接是否正常（PC端）
3. 终端应用是否有root权限（手机端）
4. 脚本文件是否有执行权限
5. 游戏是否处于可操作状态
6. 坐标是否适配当前设备分辨率

---

**项目维护**: 两个文件的参数已完全一致，确保了行为的统一性！

---

## 更新记录

### 2025-07-26 - 新增触摸参数记录器功能
- **修改内容**: 在move_debugger.py中新增TouchEventRecorder类和相关功能
- **修改原因**: 用户需要记录滑动和点击参数，自动生成可用的SWIPE和TAP命令
- **负责人**: AI Assistant (Augment Agent)
- **修改时间**: 2025-07-26

#### 新增功能详情:
1. **TouchEventRecorder类**: 完整的触摸事件监听和记录系统
2. **触摸事件监听**: 使用ADB getevent命令实时监听屏幕触摸
3. **参数自动记录**:
   - 滑动参数: 起始坐标、结束坐标、持续时间
   - 点击参数: 点击坐标
4. **命令格式生成**: 自动生成标准的SWIPE和TAP命令格式
5. **数据管理**: 支持查看、保存、清空、测试记录的命令
6. **文件输出**: 同时生成文本和JSON格式的命令文件

#### 主菜单新增选项:
- **7. 触摸参数记录器**: 进入触摸事件记录和管理界面

#### 技术实现:
- 使用`adb shell getevent`监听触摸设备事件
- 解析ABS_MT_POSITION_X/Y坐标和BTN_TOUCH按键事件
- 智能区分点击和滑动操作（基于移动距离）
- 生成与现有系统兼容的命令格式

#### 生成文件:
- `touch_commands.txt`: 可直接使用的命令文本文件
- `touch_commands.json`: 详细的JSON格式记录文件

#### 版本控制:
- 文件版本: move_debugger.py v1.1 → v1.2
- 新增导入模块: threading, re, json, datetime

### 2025-07-26 - 专用录制模式技术探索与功能调整
- **修改内容**: 尝试实现高级专用录制模式，最终因技术限制而停用
- **修改原因**: 探索复杂操作序列录制的可行性，包括连续拖拽轨迹和多点触控
- **负责人**: AI Assistant (Augment Agent)
- **修改时间**: 2025-07-26

#### 技术探索过程:
1. **专用录制模式开发**: 实现了滑动/点击分离的录制架构
2. **连续轨迹采样**: 将采样点转换为连续滑动命令序列
3. **时间戳驱动控制**: 精确记录和重现操作时序
4. **智能操作识别**: 自动区分滑动和点击操作类型

#### 遇到的技术限制:
1. **多点触控复杂性**: TRACKING_ID事件关联极其困难且不可靠
2. **状态跟踪失效**: 连续触摸事件在复杂场景下频繁出错
3. **方案根本缺陷**: 基于getevent的技术路线对高级操作支持有限

#### 最终决策:
- ❌ **停用高级录制功能**: 因技术架构限制无法可靠实现
- ✅ **保留基础录制功能**: 简单模式录制完全可用且稳定
- ✅ **完善现有架构**: 优化坐标转换和命令生成逻辑

#### 项目价值总结:
- **实用价值**: 基础录制功能满足日常自动化需求
- **技术价值**: 深入探索了Android触摸事件处理的复杂性
- **经验价值**: 明确了当前技术方案的能力边界和限制
- 保持向后兼容性，不影响现有功能
